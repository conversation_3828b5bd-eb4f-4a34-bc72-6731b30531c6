"use client";
import Len<PERSON> from "lenis";
import { useScroll } from "motion/react";
import { useEffect, useRef } from "react";
import { Section1 } from "@/components/perspective-sect/section-1";
import { Section2 } from "@/components/perspective-sect/section-2";

export default function Home() {
  const container = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: container,
    offset: ["start start", "end end"],
  });

  useEffect(() => {
    const lenis = new Lenis();

    function raf(time: number) {
      lenis.raf(time);
      requestAnimationFrame(raf);
    }

    requestAnimationFrame(raf);
  }, []);
  return (
    <div ref={container} className="relative bg-white h-[200vh]">
      <Section1 scrollYProgress={scrollYProgress} />
      <Section2 scrollYProgress={scrollYProgress} />
    </div>
  );
}
