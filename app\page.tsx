"use client";
import Lenis from "@studio-freight/lenis";
import { AnimatePresence } from "motion/react";
import { useEffect, useState } from "react";
import Preloader from "@/components/Preloader/preloader";

export default function page() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const lenis = new Lenis();

    function raf(time: number) {
      lenis.raf(time);
      requestAnimationFrame(raf);
    }
    setTimeout(() => {
      setIsLoading(false);
      document.body.style.cursor = "default";
      window.scrollTo(0, 0);
    }, 2000);
    requestAnimationFrame(raf);
  }, []);
  return (
    <div>
      <AnimatePresence mode="wait">
        {isLoading && <Preloader />}
      </AnimatePresence>
      page
    </div>
  );
}
