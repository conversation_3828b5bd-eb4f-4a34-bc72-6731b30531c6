{"name": "taps", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build --turbopack", "start": "next start", "lint": "biome check", "format": "biome format --write"}, "dependencies": {"@studio-freight/lenis": "^1.0.42", "gsap": "^3.13.0", "lenis": "^1.3.8", "motion": "^12.23.12", "next": "15.5.0", "next-view-transitions": "^0.3.4", "react": "19.1.0", "react-dom": "19.1.0", "sass": "^1.90.0"}, "devDependencies": {"@biomejs/biome": "2.2.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}